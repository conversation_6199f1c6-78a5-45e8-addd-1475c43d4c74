import pandas as pd

p = pd.read_csv('/home/<USER>/repo/disengagement/data/2025_0312_productivity_weekly_with_control_variables_fillna.csv')
p

merged_productivity_data = pd.read_csv('../result/20250731_did_result_psm_local_matching/productivity_with_propensity_scores_with_attritions_365.csv')
merged_productivity_data

# Remove columns feature_sigmod_8 to feature_sigmod_24 from merged_productivity_data if they exist
cols_to_remove = [f'feature_sigmod_{n}' for n in range(8, 25)]
merged_productivity_data = merged_productivity_data.drop(columns=[col for col in cols_to_remove if col in merged_productivity_data.columns])

merged_productivity_data

merged_productivity_data_attrition = merged_productivity_data[merged_productivity_data['someone_left'] == 1]
merged_productivity_data_attrition = merged_productivity_data_attrition[merged_productivity_data_attrition['feature_sigmod_add'].notnull()]
merged_productivity_data_attrition = merged_productivity_data_attrition[merged_productivity_data_attrition['feature_sigmod_add'] != 0.5]
merged_productivity_data_attrition

p_test = pd.read_csv('../result/p_test.csv')
p_test

p_vary_time_attrition = pd.read_csv('../result/did_result_20250227/p_vary_time_attrition_20250227.csv')
p_vary_time_attrition




productivity_data_20250730 = pd.read_csv('../result/20250730_did_result/productivity_with_propensity_scores_with_attritions_365.csv')

productivity_data_20250730

productivity_data_20250227 = pd.read_csv('../result/p_test.csv')
productivity_data_20250227

productivity_data_20250310 = pd.read_csv('../result/did_result_20250310/productivity_20250310_with_propensity_scores_with_attritions.csv')
productivity_data_20250310

attritions = pd.read_csv('/home/<USER>/repo/disengagement/data/attrition_csv/attritions_add_burst_merged_365.csv')
attritions





did_data_2 = pd.read_csv('/home/<USER>/repo/disengagement/result/did_result_20250227/compiled_data_test.csv')
did_data_2

did_data = pd.read_csv('/home/<USER>/repo/disengagement/result/20250731_did_result_psm_local_matching/compiled_data_test_limit365_processed.csv')
did_data

# 比较did_data与did_data_2 比较每一个cohort的区别(依据cohort_id)
# Compare cohort coverage
cohorts_1 = set(did_data['cohort_id'].unique())
cohorts_2 = set(did_data_2['cohort_id'].unique())

print(f"Cohorts in did_data: {len(cohorts_1)}")
print(f"Cohorts in did_data_2: {len(cohorts_2)}")
print(f"Common cohorts: {len(cohorts_1 & cohorts_2)}")
print(f"Only in did_data: {len(cohorts_1 - cohorts_2)}")
print(f"Only in did_data_2: {len(cohorts_2 - cohorts_1)}")

# Compare each common cohort
common_cohorts = cohorts_1 & cohorts_2
for cohort_id in sorted(common_cohorts):
    cohort_1 = did_data[did_data['cohort_id'] == cohort_id]
    cohort_2 = did_data_2[did_data_2['cohort_id'] == cohort_id]
    
    print(f"\nCohort {cohort_id}:")
    print(f"  did_data: {len(cohort_1)} rows")
    print(f"  did_data_2: {len(cohort_2)} rows")
    
    # Compare key columns if they exist
    if 'repo_name' in cohort_1.columns and 'repo_name' in cohort_2.columns:
        repos_1 = set(cohort_1['repo_name'].unique())
        repos_2 = set(cohort_2['repo_name'].unique())
        print(f"  Repos: {len(repos_1)} vs {len(repos_2)}")